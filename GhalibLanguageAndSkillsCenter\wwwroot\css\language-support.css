/* Language-specific styling */

/* RTL specific styles */
.rtl {
    direction: rtl;
    text-align: right;
}

.rtl .navbar-brand {
    margin-right: 0;
    margin-left: auto;
}

.rtl .dropdown-menu {
    right: 0;
    left: auto;
}

.rtl .nav-item {
    text-align: right;
}

.rtl .top-row {
    justify-content: flex-start;
}

.rtl .me-3 {
    margin-left: 1rem !important;
    margin-right: 0 !important;
}

.rtl .ms-4 {
    margin-right: 1.5rem !important;
    margin-left: 0 !important;
}

/* LTR specific styles */
.ltr {
    direction: ltr;
    text-align: left;
}

.ltr .navbar-brand {
    margin-left: 0;
    margin-right: auto;
}

.ltr .dropdown-menu {
    left: 0;
    right: auto;
}

.ltr .nav-item {
    text-align: left;
}

.ltr .top-row {
    justify-content: flex-end;
}

.ltr .me-3 {
    margin-right: 1rem !important;
    margin-left: 0 !important;
}

.ltr .ms-4 {
    margin-left: 1.5rem !important;
    margin-right: 0 !important;
}

/* Language switcher specific styles */
.language-switcher .dropdown-item.active {
    background-color: var(--bs-primary);
    color: white;
}

.language-switcher .bi-check.invisible {
    visibility: hidden;
}

/* Sidebar adjustments for different directions */
.rtl .sidebar {
    border-left: none;
    border-right: 1px solid #dee2e6;
}

.ltr .sidebar {
    border-right: none;
    border-left: 1px solid #dee2e6;
}

/* Form adjustments */
.rtl .form-label {
    text-align: right;
}

.ltr .form-label {
    text-align: left;
}

/* Table adjustments */
.rtl table {
    direction: rtl;
}

.rtl th, .rtl td {
    text-align: right;
}

.ltr table {
    direction: ltr;
}

.ltr th, .ltr td {
    text-align: left;
}

/* Button group adjustments */
.rtl .btn-group .btn:first-child {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-top-right-radius: var(--bs-border-radius);
    border-bottom-right-radius: var(--bs-border-radius);
}

.rtl .btn-group .btn:last-child {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-top-left-radius: var(--bs-border-radius);
    border-bottom-left-radius: var(--bs-border-radius);
}

/* Navigation adjustments */
.rtl .nav-link {
    padding-right: 0.5rem;
    padding-left: 1rem;
}

.ltr .nav-link {
    padding-left: 0.5rem;
    padding-right: 1rem;
}

/* Icon adjustments */
.rtl .nav-icon {
    margin-left: 0.5rem;
    margin-right: 0;
}

.ltr .nav-icon {
    margin-right: 0.5rem;
    margin-left: 0;
}
