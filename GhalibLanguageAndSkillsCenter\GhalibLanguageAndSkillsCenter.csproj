﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>aspnet-GhalibLanguageAndSkillsCenter-c9013986-0de3-409e-8aef-d5c7a1fb1950</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Blazor.PersianDatePicker" Version="3.5.1" />
    <PackageReference Include="Dapper" Version="2.1.66" />
    <PackageReference Include="Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore" Version="9.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="9.0.0" />
    <PackageReference Include="Microsoft.Data.SqlClient" Version="6.0.2" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Localization" Version="9.0.0" />
    <PackageReference Include="TabBlazor" Version="0.14.42-beta" />
    <PackageReference Include="TabBlazor.QuickTable.EntityFramework" Version="0.14.42-beta" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Data\Migrations\" />
    <Folder Include="Migrations\" />
    <Folder Include="NewFolder\" />
  </ItemGroup>

</Project>
