﻿@page "/student"
@inject IGenericRepository<StudentModel> studentRepository
@inject IGenericRepository<EnrollByStudentModel> EnrollmentRepo
@inject IStringLocalizer<Resources.SharedResources> Localizer
@rendermode InteractiveServer
@using System.ComponentModel.DataAnnotations;
@using GhalibLanguageAndSkillsCenter.Models.Payment
@inject NavigationManager NavigationManager
@attribute [Authorize]
@if (!string.IsNullOrEmpty(message))
{
    <div class="@messageClass" role="alert">@message</div>
}

@if(isEditMode)
{
    <h3>@Localizer["EditStudent"]</h3>
}


@if (IsEnrolling)
{
    <h3>Enroll Student</h3>
    <label>Enroll Student With Id</label>
    <input type="text" value="@enroll.StudentId" class="mb-2 form-control" readonly disabled>

    <div class="form-group">
        <label>Select Program</label>
        <GhalibLanguageAndSkillsCenter.Components.ReUseAbleComponents.SelectProgram SelectedProgramId="@enroll.ProgramId"
        SelectedProgramIdChanged="OnProgramChange" />
    </div>
    <div class="form-group">
        <label>Select Shift</label>
        <GhalibLanguageAndSkillsCenter.Components.ReUseAbleComponents.SelectShift SelectedShiftId="@enroll.ShiftId"
        SelectedShiftIdChanged="OnShiftChange" />

    </div>

    <div class="form-group">
        <label>Select Section</label>
        <GhalibLanguageAndSkillsCenter.Components.ReUseAbleComponents.SelectSection SelectedSectionId="@enroll.SectionId"
        SelectedSectionIdChanged="OnSectionChanged" />

        <button type="submit" class="btn btn-dark w-50 mt-2" @onclick="AddEnrollment">Enroll Student</button>
    </div>
}
else if(isEditMode)
{
    <EditForm Model="@newStudent" OnValidSubmit="HandleSubmit" FormName="student">
        <DataAnnotationsValidator />
        <ValidationSummary />

        <div class="form-group">
            <label>Name</label>
            <InputText class="form-control" @bind-Value="newStudent.Name" />
        </div>

        <div class="form-group">
            <label>Last Name</label>
            <InputText class="form-control" @bind-Value="newStudent.LastName" />
        </div>

        <div class="form-group">
            <label>Father Name</label>
            <InputText class="form-control" @bind-Value="newStudent.FatherName" />
        </div>

        <div class="form-group">
            <label>Contact</label>
            <InputText class="form-control" @bind-Value="newStudent.Contact" />
        </div>

        <div class="form-group">
            <label>Age</label>
            <InputNumber class="form-control" @bind-Value="newStudent.Age" />
        </div>

        <div class="form-group">
            <label>TazkeraNumber</label>
            <InputText class="form-control" @bind-Value="newStudent.TazkeraNumber" />
        </div>

        <div class="form-group">
            <label>Address</label>
            <InputText class="form-control" @bind-Value="newStudent.Address" />
        </div>

        <button class="btn btn-primary mt-2" type="submit">
            @(isEditMode ? "Update Student" : "Add Student")
        </button>

        @if (isEditMode)
        {
            <button type="button" class="btn btn-secondary mt-2 ms-2" @onclick="CancelEdit">Cancel</button>
        }
    </EditForm>
}
@if (isGivingDiscount)
{
    <h3>Give Discount</h3>
    <label>Discount for Student ID: @discount.StudentId</label>

    <div class="form-group">
        <label>Select Program</label>
        <GhalibLanguageAndSkillsCenter.Components.ReUseAbleComponents.SelectProgram SelectedProgramId="@discount.ProgramId"
        SelectedProgramIdChanged="@((int id) => discount.ProgramId = id)" />
    </div>

    <div class="form-group">
        <label>Discount Percentage</label>
        <InputNumber @bind-Value="discount.DiscountPercentage" class="form-control" />
    </div>

    <div class="form-group">
        <label>Reason (optional)</label>
        <InputText @bind-Value="discount.Reason" class="form-control" />
    </div>

    <button class="btn btn-primary mt-2" @onclick="SubmitDiscount">Apply Discount</button>
    <button class="btn btn-secondary mt-2 ms-2" @onclick="CancelDiscount">Cancel</button>
}


<hr />
<h3>Student List</h3>

<!-- Search box -->
<div class="form-group mb-3">
    <label for="searchInput">Search Students</label>
    <input type="text" id="searchInput" class="form-control"
    @bind="searchTerm" @bind:event="oninput"
    placeholder="Search by name, last name, or contact" />
</div>

@if (FilteredStudents is not null && FilteredStudents.Any())
{
    <table class="table table-striped">
        <thead>
            <tr>
                <th>@Localizer["Id"]</th>
                <th>@Localizer["Name"]</th>
                <th>@Localizer["LastName"]</th>
                <th>@Localizer["FatherName"]</th>
                <th>@Localizer["Contact"]</th>
                <th>@Localizer["Age"]</th>
                <th>@Localizer["TazkeraNumber"]</th>
                <th>@Localizer["Address"]</th>
                <th>@Localizer["Actions"]</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var student in FilteredStudents)
            {
                <tr>
                    <td>@student.StudentId</td>
                    <td>@student.Name</td>
                    <td>@student.LastName</td>
                    <td>@student.FatherName</td>
                    <td>@student.Contact</td>
                    <td>@student.Age</td>
                    <td>@student.TazkeraNumber</td>
                    <td>@student.Address</td>
                    <td>
                        <button class="btn btn-warning btn-sm" @onclick="() => EditStudent(student)">@Localizer["Edit"]</button>
                        <button class="btn btn-danger btn-sm ms-2" @onclick="() => ConfirmDelete(student.StudentId)">@Localizer["Delete"]</button>
                        <button class="btn btn-dark btn-sm ms-2" @onclick="()=>enrollStudent(student.StudentId)">Enroll</button>
                        <button class="btn btn-success btn-sm ms-2" @onclick="() => StartGivingDiscount(student.StudentId)">Discount</button>


                    </td>
                </tr>
            }
        </tbody>
    </table>
}
else
{
    <p>No students found.</p>
}

@code {
    private StudentModel newStudent = new();
    private List<StudentModel> students = new();
    private bool isEditMode = false;
    private EnrollByStudentModel enroll = new();
    private bool IsEnrolling = false;
    private string? message;
    private string? messageClass;
    private bool isGivingDiscount = false;
    private DiscountModel discount = new();


    private string searchTerm = string.Empty;

    private IEnumerable<StudentModel> FilteredStudents =>
        string.IsNullOrWhiteSpace(searchTerm)
            ? students
            : students.Where(s =>
                (s.Name?.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ?? false) ||
                (s.LastName?.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ?? false) ||
                (s.Contact?.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ?? false));

    protected override async Task OnInitializedAsync()
    {
        await LoadStudents();
    }

    private async Task LoadStudents()
    {
        students = (await studentRepository.GetAllAsync("GetAllStudentDetails")).ToList();
    }

    private async Task HandleSubmit()
    {
        if (isEditMode)
        {
            await studentRepository.UpdateAsync("UpdateStudentAndStudentDetails", new
            {
                newStudent.StudentId,
                newStudent.Name,
                newStudent.LastName,
                newStudent.FatherName,
                newStudent.Contact,
                newStudent.StudentDetailsId,
                newStudent.Age,
                newStudent.TazkeraNumber,
                newStudent.Address
            });
            message = "Student updated successfully.";
            messageClass = "alert alert-info";
        }
        else
        {
            await studentRepository.AddAsync("AddStudent", new
            {
                newStudent.Name,
                newStudent.LastName,
                newStudent.FatherName,
                newStudent.Contact
            });
            message = "Student added successfully.";
            messageClass = "alert alert-success";
        }

        newStudent = new();
        isEditMode = false;
        await LoadStudents();
    }


    private void EditStudent(StudentModel student)
    {
        isEditMode = true;
        if (IsEnrolling)
        {
            IsEnrolling = false;
        }
        newStudent = new StudentModel
            {
                StudentId = student.StudentId,
                Name = student.Name,
                LastName = student.LastName,
                FatherName = student.FatherName,
                Contact = student.Contact,
                StudentDetailsId = student.StudentDetailsId,
                Age = student.Age,
                TazkeraNumber = student.TazkeraNumber,
                Address = student.Address
            };
    }

    private async Task ConfirmDelete(int studentId)
    {
        if (await JS.InvokeAsync<bool>("confirm", Localizer["AreYouSureDelete"]))
        {
            await DeleteStudent(studentId);
        }
    }

    private void enrollStudent(int StudentId)
    {
        IsEnrolling = true;
        enroll.StudentId = StudentId;
        if(isEditMode)
        {
            isEditMode = false;
        }
    }

    private async Task DeleteStudent(int id)
    {
        await studentRepository.DeleteAsync("DeleteStudent", new { StudentId = id });
        await LoadStudents();
        message = "Student Deleted successfully.";
        messageClass = "alert alert-danger";
    }

    private void CancelEdit()
    {
        isEditMode = false;
        newStudent = new();
    }
    private void StartGivingDiscount(int studentId)
    {
        isGivingDiscount = true;
        if (isEditMode) isEditMode = false;
        if (IsEnrolling) IsEnrolling = false;

        discount = new DiscountModel
            {
                StudentId = studentId
            };
    }

    private async Task SubmitDiscount()
    {
        if (discount.ProgramId == 0)
        {
            message = "Please select a program.";
            messageClass = "alert alert-warning";
            return;
        }

        await studentRepository.AddAsync("AddStudentDiscount", new
        {
            discount.DiscountPercentage,
            discount.Reason,
            discount.StudentId,
            discount.ProgramId
        });

        isGivingDiscount = false;
        message = "Discount applied successfully.";
        messageClass = "alert alert-success";
    }

    private void CancelDiscount()
    {
        isGivingDiscount = false;
        discount = new();
    }




    private void OnSectionChanged(int id)
    {
        enroll.SectionId = id;
    }

    private void OnProgramChange(int id)
    {
        enroll.ProgramId = id;
    }

    private async Task AddEnrollment()
    {
        await EnrollmentRepo.AddAsync("EnrollStudent", new
        {
            enroll.StudentId,
            enroll.ProgramId,
            enroll.SectionId,
            enroll.ShiftId,
            enroll.EnrollmentDate
        });

        IsEnrolling = false;
        message = "Student enrolled successfully.";
        messageClass = "alert alert-success";
    }
    public void OnShiftChange(int id){
        enroll.ShiftId = id;
    }
    [Inject]
    private IJSRuntime JS { get; set; }
    public class EnrollByStudentModel
    {
        [Required(ErrorMessage = "Please select a student.")]
        public int StudentId { get; set; }

        [Required(ErrorMessage = "Please select a program.")]
        public int ProgramId { get; set; }

        [Required(ErrorMessage = "Please select a section.")]
        public int SectionId { get; set; }

        [Required(ErrorMessage = "Please select a Shift.")]
        public int ShiftId { get; set; }

        public DateTime EnrollmentDate { get; set; } = DateTime.Today;
    }
}
