@using GhalibLanguageAndSkillsCenter.Services
@using Microsoft.Extensions.Localization
@inject LanguageService LanguageService
@inject IStringLocalizer<Resources.SharedResources> Localizer
@inject IJSRuntime JSRuntime
@implements IDisposable
@rendermode InteractiveServer

<div class="dropdown language-switcher">
    <button class="btn btn-outline-secondary dropdown-toggle @(_isChangingLanguage ? "disabled" : "")"
            type="button"
            id="languageDropdown"
            data-bs-toggle="dropdown"
            aria-expanded="false"
            disabled="@_isChangingLanguage">
        @if (_isChangingLanguage)
        {
            <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
        }
        else
        {
            <i class="bi bi-globe me-1"></i>
        }
        @Localizer["Language"]
    </button>
    <ul class="dropdown-menu" aria-labelledby="languageDropdown">
        <li>
            <button class="dropdown-item @(LanguageService.CurrentLanguage == "en" ? "active" : "")"
                    @onclick="ChangeToEnglish"
                    disabled="@_isChangingLanguage">
                <i class="bi bi-check me-2 @(LanguageService.CurrentLanguage == "en" ? "" : "invisible")"></i>
                @Localizer["English"]
            </button>
        </li>
        <li>
            <button class="dropdown-item @(LanguageService.CurrentLanguage == "fa" ? "active" : "")"
                    @onclick="ChangeToPersian"
                    disabled="@_isChangingLanguage">
                <i class="bi bi-check me-2 @(LanguageService.CurrentLanguage == "fa" ? "" : "invisible")"></i>
                @Localizer["Persian"]
            </button>
        </li>
    </ul>
</div>

@code {
    private bool _isChangingLanguage = false;

    protected override async Task OnInitializedAsync()
    {
        LanguageService.LanguageChanged += OnLanguageChanged;
        StateHasChanged(); // Ensure current language is displayed
    }

    private async Task ChangeLanguage(string language)
    {
        if (_isChangingLanguage || LanguageService.CurrentLanguage == language)
            return;

        _isChangingLanguage = true;
        StateHasChanged();

        try
        {
            await LanguageService.SetLanguageAsync(language);
            // Small delay to ensure the language is saved
            await Task.Delay(100);
            // Reload the page to apply the new language and direction
            await JSRuntime.InvokeVoidAsync("location.reload");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error changing language: {ex.Message}");
            _isChangingLanguage = false;
            StateHasChanged();
        }
    }

    private async Task ChangeToEnglish()
    {
        await ChangeLanguage("en");
    }

    private async Task ChangeToPersian()
    {
        await ChangeLanguage("fa");
    }

    private void OnLanguageChanged(string language)
    {
        InvokeAsync(StateHasChanged);
    }

    public void Dispose()
    {
        LanguageService.LanguageChanged -= OnLanguageChanged;
    }
}
