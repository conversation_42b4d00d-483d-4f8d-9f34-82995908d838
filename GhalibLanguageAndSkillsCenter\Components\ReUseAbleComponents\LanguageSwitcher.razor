@using GhalibLanguageAndSkillsCenter.Services
@using Microsoft.Extensions.Localization
@inject LanguageService LanguageService
@inject IStringLocalizer<Resources.SharedResources> Localizer
@inject IJSRuntime JSRuntime
@implements IDisposable

<div class="dropdown language-switcher">
    <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="languageDropdown" data-bs-toggle="dropdown" aria-expanded="false">
        <i class="bi bi-globe me-1"></i>
        @Localizer["Language"]
    </button>
    <ul class="dropdown-menu" aria-labelledby="languageDropdown">
        <li>
            <button class="dropdown-item @(LanguageService.CurrentLanguage == "en" ? "active" : "")"
                    @onclick="ChangeToEnglish">
                <i class="bi bi-check me-2 @(LanguageService.CurrentLanguage == "en" ? "" : "invisible")"></i>
                @Localizer["English"]
            </button>
        </li>
        <li>
            <button class="dropdown-item @(LanguageService.CurrentLanguage == "fa" ? "active" : "")"
                    @onclick="ChangeToPersian">
                <i class="bi bi-check me-2 @(LanguageService.CurrentLanguage == "fa" ? "" : "invisible")"></i>
                @Localizer["Persian"]
            </button>
        </li>
    </ul>
</div>

@code {
    protected override async Task OnInitializedAsync()
    {
        LanguageService.LanguageChanged += OnLanguageChanged;
        await LanguageService.InitializeAsync();
    }

    private async Task ChangeLanguage(string language)
    {
        await LanguageService.SetLanguageAsync(language);
        // Reload the page to apply the new language and direction
        await JSRuntime.InvokeVoidAsync("location.reload");
    }

    private async Task ChangeToEnglish()
    {
        await ChangeLanguage("en");
    }

    private async Task ChangeToPersian()
    {
        await ChangeLanguage("fa");
    }

    private void OnLanguageChanged(string language)
    {
        InvokeAsync(StateHasChanged);
    }

    public void Dispose()
    {
        LanguageService.LanguageChanged -= OnLanguageChanged;
    }
}
