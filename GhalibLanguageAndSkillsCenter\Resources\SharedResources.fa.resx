<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>

  <!-- Navigation -->
  <data name="AppTitle" xml:space="preserve">
    <value>مرکز زبان و مهارت‌های غالب</value>
  </data>
  <data name="Home" xml:space="preserve">
    <value>خانه</value>
  </data>
  <data name="Enrollment" xml:space="preserve">
    <value>پذیرش</value>
  </data>
  <data name="Students" xml:space="preserve">
    <value>شاگردان</value>
  </data>
  <data name="Register" xml:space="preserve">
    <value>ثبت نام</value>
  </data>
  <data name="Finance" xml:space="preserve">
    <value>مالی</value>
  </data>
  <data name="Payments" xml:space="preserve">
    <value>پرداخت‌ها</value>
  </data>
  <data name="Reports" xml:space="preserve">
    <value>گزارشات</value>
  </data>
  <data name="Timetable" xml:space="preserve">
    <value>تقسیم اوقات</value>
  </data>
  <data name="Staff" xml:space="preserve">
    <value>مدیریت کارمندان</value>
  </data>
  <data name="Settings" xml:space="preserve">
    <value>تنظیمات</value>
  </data>

  <!-- Authentication -->
  <data name="Hello" xml:space="preserve">
    <value>سلام</value>
  </data>
  <data name="CreateUser" xml:space="preserve">
    <value>ایجاد یوزر</value>
  </data>
  <data name="Logout" xml:space="preserve">
    <value>خروج</value>
  </data>
  <data name="Login" xml:space="preserve">
    <value>ورود</value>
  </data>

  <!-- Common -->
  <data name="Language" xml:space="preserve">
    <value>زبان</value>
  </data>
  <data name="English" xml:space="preserve">
    <value>English</value>
  </data>
  <data name="Persian" xml:space="preserve">
    <value>فارسی</value>
  </data>
  <data name="NavigationMenu" xml:space="preserve">
    <value>منوی ناوبری</value>
  </data>

  <!-- Error Messages -->
  <data name="UnknownError" xml:space="preserve">
    <value>یک خطای ناشناخته رخ داده است.</value>
  </data>
  <data name="Reload" xml:space="preserve">
    <value>بارگذاری مجدد</value>
  </data>

  <!-- Forms -->
  <data name="Name" xml:space="preserve">
    <value>نام</value>
  </data>
  <data name="LastName" xml:space="preserve">
    <value>نام خانوادگی</value>
  </data>
  <data name="FatherName" xml:space="preserve">
    <value>نام پدر</value>
  </data>
  <data name="Age" xml:space="preserve">
    <value>سن</value>
  </data>
  <data name="ProgramName" xml:space="preserve">
    <value>نام برنامه</value>
  </data>
  <data name="Fee" xml:space="preserve">
    <value>هزینه</value>
  </data>
  <data name="StartingDate" xml:space="preserve">
    <value>تاریخ شروع</value>
  </data>
  <data name="EndingDate" xml:space="preserve">
    <value>تاریخ پایان</value>
  </data>
  <data name="Submit" xml:space="preserve">
    <value>ارسال</value>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>ویرایش</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>حذف</value>
  </data>
  <data name="Actions" xml:space="preserve">
    <value>عملیات</value>
  </data>

  <!-- Settings -->
  <data name="ManageDepartments" xml:space="preserve">
    <value>مدیریت بخش‌ها</value>
  </data>
  <data name="ManageCountries" xml:space="preserve">
    <value>مدیریت کشورها</value>
  </data>
  <data name="ManagePositions" xml:space="preserve">
    <value>مدیریت مقام‌ها</value>
  </data>
  <data name="ManageProvinces" xml:space="preserve">
    <value>مدیریت ولایات</value>
  </data>
  <data name="ManagePrograms" xml:space="preserve">
    <value>مدیریت برنامه‌ها</value>
  </data>
  <data name="ManageSubjects" xml:space="preserve">
    <value>مدیریت مضامین</value>
  </data>
  <data name="AccountSettings" xml:space="preserve">
    <value>تنظیمات حساب</value>
  </data>
  <data name="AddEditDeleteDepartments" xml:space="preserve">
    <value>اضافه، ویرایش یا حذف بخش‌ها.</value>
  </data>
  <data name="AddEditDeleteCountries" xml:space="preserve">
    <value>اضافه، ویرایش یا حذف کشورها.</value>
  </data>
  <data name="AddEditDeletePositions" xml:space="preserve">
    <value>اضافه، ویرایش یا حذف مقام‌ها.</value>
  </data>
  <data name="AddEditDeleteProvinces" xml:space="preserve">
    <value>اضافه، ویرایش یا حذف ولایات.</value>
  </data>
  <data name="AddEditDeletePrograms" xml:space="preserve">
    <value>اضافه، ویرایش یا حذف برنامه‌ها.</value>
  </data>
  <data name="AddEditDeleteSubjects" xml:space="preserve">
    <value>اضافه، ویرایش یا حذف مضامین.</value>
  </data>
  <data name="ControlAccountSettings" xml:space="preserve">
    <value>کنترل تنظیمات حساب شما.</value>
  </data>

  <!-- Student Management -->
  <data name="EditStudent" xml:space="preserve">
    <value>ویرایش شاگرد</value>
  </data>
  <data name="EnrollStudent" xml:space="preserve">
    <value>ثبت نام شاگرد</value>
  </data>
  <data name="Id" xml:space="preserve">
    <value>شناسه</value>
  </data>
  <data name="Contact" xml:space="preserve">
    <value>تماس</value>
  </data>
  <data name="TazkeraNumber" xml:space="preserve">
    <value>شماره تذکره</value>
  </data>
  <data name="Address" xml:space="preserve">
    <value>آدرس</value>
  </data>
  <data name="AreYouSureDelete" xml:space="preserve">
    <value>آیا مطمئن هستید که می‌خواهید این شاگرد را حذف کنید؟</value>
  </data>

  <!-- Payment -->
  <data name="Payment" xml:space="preserve">
    <value>پرداخت</value>
  </data>
  <data name="AddStudentPayment" xml:space="preserve">
    <value>اضافه کردن پرداخت شاگرد</value>
  </data>
  <data name="PayAmount" xml:space="preserve">
    <value>مبلغ پرداخت</value>
  </data>
  <data name="PaymentDate" xml:space="preserve">
    <value>تاریخ پرداخت</value>
  </data>
  <data name="SavePayment" xml:space="preserve">
    <value>ذخیره پرداخت</value>
  </data>
  <data name="RemainingAmount" xml:space="preserve">
    <value>مبلغ باقی‌مانده</value>
  </data>
  <data name="PaymentSavedSuccessfully" xml:space="preserve">
    <value>پرداخت با موفقیت ذخیره شد. مبلغ باقی‌مانده: {0}.</value>
  </data>
  <data name="OverpaymentError" xml:space="preserve">
    <value>پرداخت اضافی: شما فقط می‌توانید تا {0} پرداخت کنید.</value>
  </data>
  <data name="PaymentRecordedSuccessfully" xml:space="preserve">
    <value>پرداخت با موفقیت ثبت شد.</value>
  </data>
  <data name="CannotProcessPayment" xml:space="preserve">
    <value>نمی‌توان پرداخت را پردازش کرد: شاگرد فقط {0} بیشتر بدهکار است.</value>
  </data>
  <data name="UnexpectedError" xml:space="preserve">
    <value>خطای غیرمنتظره‌ای رخ داد. لطفاً دوباره تلاش کنید.</value>
  </data>

  <!-- Common UI -->
  <data name="Loading" xml:space="preserve">
    <value>در حال بارگذاری…</value>
  </data>
  <data name="NoDataFound" xml:space="preserve">
    <value>هیچ داده‌ای یافت نشد.</value>
  </data>
  <data name="NoPaymentHistoryFound" xml:space="preserve">
    <value>هیچ تاریخچه پرداختی یافت نشد.</value>
  </data>
  <data name="Success" xml:space="preserve">
    <value>موفقیت</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>خطا</value>
  </data>
  <data name="Save" xml:space="preserve">
    <value>ذخیره</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>لغو</value>
  </data>
  <data name="Close" xml:space="preserve">
    <value>بستن</value>
  </data>
  <data name="Add" xml:space="preserve">
    <value>اضافه</value>
  </data>
  <data name="Update" xml:space="preserve">
    <value>به‌روزرسانی</value>
  </data>
  <data name="Search" xml:space="preserve">
    <value>جستجو</value>
  </data>

  <!-- Subjects -->
  <data name="Subjects" xml:space="preserve">
    <value>مضامین</value>
  </data>

  <!-- Authentication -->
  <data name="YouAreAuthenticated" xml:space="preserve">
    <value>شما احراز هویت شده‌اید</value>
  </data>

  <!-- Counter -->
  <data name="Counter" xml:space="preserve">
    <value>شمارنده</value>
  </data>
  <data name="CurrentCount" xml:space="preserve">
    <value>شمارش فعلی: {0}</value>
  </data>
  <data name="ClickMe" xml:space="preserve">
    <value>کلیک کنید</value>
  </data>
</root>
