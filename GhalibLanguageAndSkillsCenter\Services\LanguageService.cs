using Microsoft.JSInterop;
using System.Globalization;

namespace GhalibLanguageAndSkillsCenter.Services
{
    public class LanguageService
    {
        private readonly IJSRuntime _jsRuntime;
        private const string LANGUAGE_KEY = "selectedLanguage";

        public event Action<string>? LanguageChanged;

        public LanguageService(IJSRuntime jsRuntime)
        {
            _jsRuntime = jsRuntime;
        }

        public string CurrentLanguage { get; private set; } = "en"; // Default to English

        public bool IsRtl => CurrentLanguage == "fa";

        public string Direction => IsRtl ? "rtl" : "ltr";

        public async Task<string> GetLanguageAsync()
        {
            try
            {
                var language = await _jsRuntime.InvokeAsync<string>("localStorage.getItem", LANGUAGE_KEY);
                if (!string.IsNullOrEmpty(language) && (language == "en" || language == "fa"))
                {
                    CurrentLanguage = language;
                }
            }
            catch
            {
                // If localStorage is not available, use default
                CurrentLanguage = "en";
            }

            return CurrentLanguage;
        }

        public async Task SetLanguageAsync(string language)
        {
            if (language != "en" && language != "fa")
                throw new ArgumentException("Language must be 'en' or 'fa'");

            CurrentLanguage = language;

            try
            {
                await _jsRuntime.InvokeVoidAsync("localStorage.setItem", LANGUAGE_KEY, language);

                // Update document direction and Bootstrap CSS
                await _jsRuntime.InvokeVoidAsync("languageSwitcher.updateDocument", language, Direction);
            }
            catch
            {
                // If JavaScript is not available, continue without DOM updates
            }

            // Set the culture for the current thread
            var culture = new CultureInfo(language == "fa" ? "fa-IR" : "en-US");
            CultureInfo.CurrentCulture = culture;
            CultureInfo.CurrentUICulture = culture;

            LanguageChanged?.Invoke(language);
        }

        public async Task InitializeAsync()
        {
            var savedLanguage = await GetLanguageAsync();
            CurrentLanguage = savedLanguage;

            // Set the culture for the current thread
            var culture = new CultureInfo(CurrentLanguage == "fa" ? "fa-IR" : "en-US");
            CultureInfo.CurrentCulture = culture;
            CultureInfo.CurrentUICulture = culture;

            try
            {
                // Update document direction and Bootstrap CSS
                await _jsRuntime.InvokeVoidAsync("languageSwitcher.updateDocument", CurrentLanguage, Direction);
            }
            catch
            {
                // If JavaScript is not available, continue without DOM updates
            }

            LanguageChanged?.Invoke(CurrentLanguage);
        }
    }
}
