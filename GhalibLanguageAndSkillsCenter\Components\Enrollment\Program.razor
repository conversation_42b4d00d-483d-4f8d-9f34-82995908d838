﻿@page "/program"
@inject IGenericRepository<ProgramModel> programRepository
@inject IGenericRepository<DepartmentModel> departmentRepository
@inject IJSRuntime JS
@rendermode InteractiveServer
@attribute [Authorize]



<h3>Program</h3>

<EditForm Model="@newProgram" OnValidSubmit="HandleSubmit" FormName="program">
    <DataAnnotationsValidator />
    <ValidationSummary />

    <div class="form-group">
        <label>Program Name</label>
        <InputText class="form-control" @bind-Value="newProgram.Name" />
    </div>

    <div class="form-group">
        <label>Fee</label>
        <InputNumber class="form-control" @bind-Value="newProgram.Fee" />
    </div>

    <div class="form-group">
        <label>Starting Date</label>
        <InputPersianDatePicker @bind-Value="startingDate"
                                CssClass="form-control" />
    </div>

    <div class="form-group">
        <label>Ending Date</label>
        <InputPersianDatePicker  @bind-Value="endingDate"
                                CssClass="form-control" />
    </div>

    <div class="form-group">
        <label>Select Department</label>
        <InputSelect class="form-control" @bind-Value="newProgram.DepartmentId">
            <option value="">-- Select Department --</option>
            @foreach (var dept in departments)
            {
                <option value="@dept.DepartmentId">@dept.EnglishName</option>
            }
        </InputSelect>
    </div>

    <button class="btn btn-primary mt-2" type="submit">
        @(isEditMode ? "Update Program" : "Add Program")
    </button>
    @if (isEditMode)
    {
        <button type="button" class="btn btn-secondary mt-2 ms-2" @onclick="CancelEdit">Cancel</button>
    }
</EditForm>

<hr />

<h3>Programs List</h3>

@if (programList.Any())
{
    <table class="table table-striped">
        <thead>
            <tr>
                <th>نام پروگرام</th>
                <th>فیس</th>
                <th>تاریخ شروع</th>
                <th>تاریخ ختم</th>
                <th>مدت زمان</th>
                <th>دیپارتمنت</th>
                <th>دوره</th>
                <th>وضعیت</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var p in programList)
            {
                <tr>
                    <td>@p.Name</td>
                    <td>@p.Fee</td>
                    <td>@PersianDateConverter.ToPersianString(p.StartingDate)</td>
                    <td>@PersianDateConverter.ToPersianString(p.EndingDate)</td>
                    <td>@CalculateDuration(p.StartingDate, p.EndingDate)</td>
                    <td>@departments.FirstOrDefault(d => d.DepartmentId == p.DepartmentId)?.EnglishName</td>
                    <td>@p.Period</td>
                    <td>@(p.IsActive == 1 ? "جریان دارد" : "پایان یافته است")</td>
                    <td>
                        <button class="btn btn-warning btn-sm" @onclick="() => EditProgram(p)">Edit</button>
                        <button class="btn btn-danger btn-sm ms-2" @onclick="() => ConfirmDeleteProgram(p.ProgramId)">Delete</button>
                    </td>
                </tr>
            }
        </tbody>
    </table>
}
else
{
    <p>No programs found.</p>
}

@code {
    private ProgramModel newProgram = new();
    private List<ProgramModel> programList = new();
    private List<DepartmentModel> departments = new();
    private bool isEditMode = false;

    private string? startingDate;
    private string? endingDate;

    protected override async Task OnInitializedAsync()
    {
        departments = (await departmentRepository.GetAllAsync("AllDepartments")).ToList();
        await LoadPrograms();
    }

    private async Task LoadPrograms()
    {
        programList = (await programRepository.GetAllAsync("GetAllPrograms")).ToList();
    }

    private async Task HandleSubmit()
    {
        newProgram.StartingDate = PersianDateConverter.ToDateTime(startingDate!);
        newProgram.EndingDate = PersianDateConverter.ToDateTime(endingDate!);

        if (newProgram.DepartmentId == 0)
            return;

        if (isEditMode)
        {
            await programRepository.UpdateAsync("UpdateProgram", new
            {
                newProgram.ProgramId,
                newProgram.Name,
                newProgram.Fee,
                newProgram.StartingDate,
                newProgram.EndingDate,
                newProgram.DepartmentId
            });
        }
        else
        {
            await programRepository.AddAsync("AddProgram", new
            {
                newProgram.Name,
                newProgram.Fee,
                newProgram.StartingDate,
                newProgram.EndingDate,
                newProgram.DepartmentId
            });
        }

        newProgram = new();
        isEditMode = false;
        startingDate = endingDate = null;
        await LoadPrograms();
    }

    private void EditProgram(ProgramModel program)
    {
        isEditMode = true;
        newProgram = new ProgramModel
            {
                ProgramId = program.ProgramId,
                Name = program.Name,
                Fee = program.Fee,
                DepartmentId = program.DepartmentId
            };
        startingDate = PersianDateConverter.ToPersianString(program.StartingDate);
        endingDate = PersianDateConverter.ToPersianString(program.EndingDate);
    }

    private async Task ConfirmDeleteProgram(int id)
    {
        if (await JS.InvokeAsync<bool>("confirm", "Are you sure you want to delete?"))
        {
            await programRepository.DeleteAsync("DeleteProgram", new { ProgramId = id });
            await LoadPrograms();
        }
    }

    private void CancelEdit()
    {
        isEditMode = false;
        newProgram = new();
        startingDate = endingDate = null;
    }

    private int CalculateDuration(DateTime start, DateTime end)
        => (end - start).Days;
}
