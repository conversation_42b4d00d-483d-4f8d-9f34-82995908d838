﻿@page "/counter"
@inject IStringLocalizer<Resources.SharedResources> Localizer
@rendermode InteractiveServer

<PageTitle>@Localizer["Counter"]</PageTitle>

<AuthorizeView Roles="Administrator">
<h1>@Localizer["Counter"]</h1>

<p role="status">@string.Format(Localizer["CurrentCount"], currentCount)</p>

<button class="btn btn-primary" @onclick="IncrementCount">@Localizer["ClickMe"]</button>
</AuthorizeView>

@code {
    private int currentCount = 0;

    private void IncrementCount()
    {
        currentCount++;
    }
}
