﻿namespace GhalibLanguageAndSkillsCenter.Models.Payment
{
    public class StudentPaymentModel
    {
        public int StudentPaymentSummaryId { get; set; }
        public int StudentId { get; set; }
        public string StudentName { get; set; }
        public int ProgramId { get; set; }
        public string ProgramName { get; set; }
        public int OriginalFee { get; set; }
        public decimal DiscountPercentage { get; set; }
        public int FinalFee { get; set; }
        public int TotalPaid { get; set; }
        public int RemainingAmount { get; set; }
        public int StudentPaymentDetailId { get; set; }
        public int PaidAmount { get; set; }
        public int Installment { get; set; }
        public DateTime PaymentDate { get; set; } = DateTime.Today;
    }
}
