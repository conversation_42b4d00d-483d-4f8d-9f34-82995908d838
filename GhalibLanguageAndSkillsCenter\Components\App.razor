﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <base href="/" />
    <!-- Bootstrap CSS will be loaded dynamically based on language -->
    <link rel="stylesheet" href="@Assets["lib/bootstrap/dist/css/bootstrap.min.css"]" id="bootstrap-css" />
    <link rel="stylesheet" href="@Assets["lib/bootstrap/dist/font/bootstrap-icons.css"]" />
    <link rel="stylesheet" href="@Assets["app.css"]" />
    <link rel="stylesheet" href="@Assets["GhalibLanguageAndSkillsCenter.styles.css"]" />
    <link rel="stylesheet" href="css/language-support.css" />
    <link href="_content/Blazor.PersianDatePicker/datepicker.css" rel="stylesheet" />
    <!-- Include TabBlazor's Tabler CSS -->
    <link href="_content/TabBlazor/tabler.min.css" rel="stylesheet" />
    <ImportMap />
    <link rel="icon" type="image/png" href="favicon.png" />
    <HeadOutlet />
</head>
<body>
    <Routes />
    <script src="_framework/blazor.web.js"></script>
    <script src="_content/TabBlazor/tabBlazor.js?v=1"></script>
    <script src="_content/Blazor.PersianDatePicker/datepicker.min.js" type="text/javascript"></script>
    <script src="lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/language-switcher.js"></script>
    <script>
        // Initialize language switcher on page load
        document.addEventListener('DOMContentLoaded', function() {
            if (window.languageSwitcher) {
                window.languageSwitcher.initialize();
            }
        });
    </script>

</body>
</html>
