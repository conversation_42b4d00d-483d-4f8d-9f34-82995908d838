﻿@inherits LayoutComponentBase
@using Microsoft.AspNetCore.Components.Authorization
@using GhalibLanguageAndSkillsCenter.Services
@using Microsoft.Extensions.Localization
@inject LanguageService LanguageService
@inject IStringLocalizer<Resources.SharedResources> Localizer
@implements IDisposable

<!DOCTYPE html>
<html lang="@LanguageService.CurrentLanguage" dir="@LanguageService.Direction">
<head>
    <!-- Add meta tags and other head content if needed -->
</head>

<body>

    <div class="page">
        <div class="sidebar" dir="@LanguageService.Direction">
            <NavMenu />
        </div>

        <main>
            <div class="top-row px-4">
                <!-- Language Switcher -->
                <div class="nav-item me-3">
                    <LanguageSwitcher />
                </div>

                <!-- AuthorizeView to show either "Hello, user!" or the About link -->
                <AuthorizeView Context="authState">
                    <Authorized>
                        <span class="nav-item px-3">
                            @Localizer["Hello"], @authState.User.Identity.Name!
                        </span>
                    </Authorized>
                </AuthorizeView>
                <AuthorizeView Roles="Administrator">
                    <NavLink class="nav-link text-primary " href="Account/Register">
                        <span class="bi bi-person-plus-fill me-2 nav-icon" aria-hidden="true"></span> @Localizer["CreateUser"]
                    </NavLink>
                </AuthorizeView>
                <!-- Login / Logout -->
                <AuthorizeView>

                        <Authorized>
                            <div class="nav-item me-3">
                                <NavLink class="nav-link text-danger" href="Account/Logout">
                                    <span class="bi bi-box-arrow-right text-danger" aria-hidden="true"></span> <!-- Red logout icon -->
                                    @Localizer["Logout"]
                                </NavLink>
                            </div>
                    </Authorized>
                    <NotAuthorized>
                        <div class="nav-item px-3">
                            <NavLink class="nav-link" href="Account/Login">
                                <span class="bi bi-person-badge-nav-menu" aria-hidden="true"></span>
                                @Localizer["Login"]
                            </NavLink>
                        </div>
                    </NotAuthorized>
                </AuthorizeView>

            </div>

            <article class="content px-4">
                @Body
            </article>
        </main>
    </div>

    <div id="blazor-error-ui" data-nosnippet>
        @Localizer["UnknownError"]
        <a href="." class="reload">@Localizer["Reload"]</a>
        <span class="dismiss">🗙</span>
    </div>

</body>
</html>

@code {
    protected override async Task OnInitializedAsync()
    {
        LanguageService.LanguageChanged += OnLanguageChanged;
        await LanguageService.InitializeAsync();
    }

    private void OnLanguageChanged(string language)
    {
        InvokeAsync(StateHasChanged);
    }

    public void Dispose()
    {
        LanguageService.LanguageChanged -= OnLanguageChanged;
    }
}
