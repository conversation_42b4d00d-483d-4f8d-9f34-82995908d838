<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  
  <!-- Navigation -->
  <data name="AppTitle" xml:space="preserve">
    <value>Ghalib Language and Skills Center</value>
  </data>
  <data name="Home" xml:space="preserve">
    <value>Home</value>
  </data>
  <data name="Enrollment" xml:space="preserve">
    <value>Enrollment</value>
  </data>
  <data name="Students" xml:space="preserve">
    <value>Students</value>
  </data>
  <data name="Register" xml:space="preserve">
    <value>Register</value>
  </data>
  <data name="Finance" xml:space="preserve">
    <value>Finance</value>
  </data>
  <data name="Payments" xml:space="preserve">
    <value>Payments</value>
  </data>
  <data name="Reports" xml:space="preserve">
    <value>Reports</value>
  </data>
  <data name="Timetable" xml:space="preserve">
    <value>Timetable</value>
  </data>
  <data name="Staff" xml:space="preserve">
    <value>Staff Management</value>
  </data>
  <data name="Settings" xml:space="preserve">
    <value>Settings</value>
  </data>
  
  <!-- Authentication -->
  <data name="Hello" xml:space="preserve">
    <value>Hello</value>
  </data>
  <data name="CreateUser" xml:space="preserve">
    <value>Create User</value>
  </data>
  <data name="Logout" xml:space="preserve">
    <value>Logout</value>
  </data>
  <data name="Login" xml:space="preserve">
    <value>Login</value>
  </data>
  
  <!-- Common -->
  <data name="Language" xml:space="preserve">
    <value>Language</value>
  </data>
  <data name="English" xml:space="preserve">
    <value>English</value>
  </data>
  <data name="Persian" xml:space="preserve">
    <value>فارسی</value>
  </data>
  <data name="NavigationMenu" xml:space="preserve">
    <value>Navigation Menu</value>
  </data>
  
  <!-- Error Messages -->
  <data name="UnknownError" xml:space="preserve">
    <value>An unknown error has occurred.</value>
  </data>
  <data name="Reload" xml:space="preserve">
    <value>Reload</value>
  </data>
  
  <!-- Forms -->
  <data name="Name" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="LastName" xml:space="preserve">
    <value>Last Name</value>
  </data>
  <data name="FatherName" xml:space="preserve">
    <value>Father Name</value>
  </data>
  <data name="Age" xml:space="preserve">
    <value>Age</value>
  </data>
  <data name="ProgramName" xml:space="preserve">
    <value>Program Name</value>
  </data>
  <data name="Fee" xml:space="preserve">
    <value>Fee</value>
  </data>
  <data name="StartingDate" xml:space="preserve">
    <value>Starting Date</value>
  </data>
  <data name="EndingDate" xml:space="preserve">
    <value>Ending Date</value>
  </data>
  <data name="Submit" xml:space="preserve">
    <value>Submit</value>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>Edit</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="Actions" xml:space="preserve">
    <value>Actions</value>
  </data>
  
  <!-- Settings -->
  <data name="ManageDepartments" xml:space="preserve">
    <value>Manage Departments</value>
  </data>
  <data name="ManageCountries" xml:space="preserve">
    <value>Manage Countries</value>
  </data>
  <data name="ManagePositions" xml:space="preserve">
    <value>Manage Positions</value>
  </data>
  <data name="ManageProvinces" xml:space="preserve">
    <value>Manage Provinces</value>
  </data>
  <data name="ManagePrograms" xml:space="preserve">
    <value>Manage Programs</value>
  </data>
  <data name="ManageSubjects" xml:space="preserve">
    <value>Manage Subjects</value>
  </data>
  <data name="AccountSettings" xml:space="preserve">
    <value>Account Settings</value>
  </data>
  <data name="AddEditDeleteDepartments" xml:space="preserve">
    <value>Add, edit, or delete departments.</value>
  </data>
  <data name="AddEditDeleteCountries" xml:space="preserve">
    <value>Add, edit, or delete countries.</value>
  </data>
  <data name="AddEditDeletePositions" xml:space="preserve">
    <value>Add, edit, or delete positions.</value>
  </data>
  <data name="AddEditDeleteProvinces" xml:space="preserve">
    <value>Add, edit, or delete provinces.</value>
  </data>
  <data name="AddEditDeletePrograms" xml:space="preserve">
    <value>Add, edit, or delete programs.</value>
  </data>
  <data name="AddEditDeleteSubjects" xml:space="preserve">
    <value>Add, edit, or delete subjects.</value>
  </data>
  <data name="ControlAccountSettings" xml:space="preserve">
    <value>Control your account settings.</value>
  </data>
</root>
