// Language switcher functionality
window.languageSwitcher = {
    // Switch Bootstrap CSS based on language direction
    switchBootstrap: function(isRtl) {
        const bootstrapLink = document.getElementById('bootstrap-css');
        if (bootstrapLink) {
            const currentHref = bootstrapLink.href;
            if (isRtl) {
                // Switch to RTL version
                if (currentHref.includes('bootstrap.min.css') && !currentHref.includes('rtl')) {
                    bootstrapLink.href = currentHref.replace('bootstrap.min.css', 'bootstrap.rtl.min.css');
                }
            } else {
                // Switch to LTR version
                if (currentHref.includes('bootstrap.rtl.min.css')) {
                    bootstrapLink.href = currentHref.replace('bootstrap.rtl.min.css', 'bootstrap.min.css');
                }
            }
        }
    },

    // Update document direction and language
    updateDocument: function(language, direction) {
        document.documentElement.lang = language;
        document.documentElement.dir = direction;
        document.body.dir = direction;

        // Update Bootstrap CSS
        this.switchBootstrap(direction === 'rtl');

        // Add/remove RTL class to body for custom styling
        if (direction === 'rtl') {
            document.body.classList.add('rtl');
            document.body.classList.remove('ltr');
        } else {
            document.body.classList.add('ltr');
            document.body.classList.remove('rtl');
        }
    }
};
