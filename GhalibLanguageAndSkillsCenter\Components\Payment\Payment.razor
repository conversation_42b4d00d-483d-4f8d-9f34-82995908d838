﻿@page "/payment"
@using GhalibLanguageAndSkillsCenter.Models.Payment
@inject IGenericRepository<StudentModel> StudentRepo
@inject IGenericRepository<ProgramModel> ProgramRepo
@inject IGenericRepository<DiscountModel> DiscountRepo
@inject IGenericRepository<StudentPaymentModel> PaymentRepo
@inject IStringLocalizer<Resources.SharedResources> Localizer
@rendermode InteractiveServer

<div class="shadow-lg p-4">
    <h3>@Localizer["Payment"]</h3>

    <div class="row mb-3">
        <div class="col-md-6">
            <!-- Program Selection -->
            <InputSelect class="form-control"
            Value="SelectedProgramId"
            ValueChanged="@(async (int id) => await OnProgramChanged(id))"
            ValueExpression="() => SelectedProgramId">
                <option value="0">-- Select program --</option>
                @foreach (var program in programs)
                {
                    <option value="@program.ProgramId">@program.Name</option>
                }
            </InputSelect>
        </div>

        <div class="col-md-6">
            <!-- Student Selection -->
            <InputSelect class="form-control"
            @bind-Value="SelectedStudentId"
            disabled="@(students == null || students.Count == 0)">
                <option value="0">-- Select student --</option>
                @if (students != null && students.Count == 0 && SelectedProgramId > 0)
                {
                    <option disabled>-- No students available --</option>
                }
                else
                {
                    @foreach (var student in students)
                    {
                        <option value="@student.StudentId">@student.Name @student.LastName</option>
                    }
                }
            </InputSelect>
        </div>
    </div>

    @if (SelectedProgramId != 0 && SelectedStudentId != 0)
    {
        <div class="row mb-3">
            <div class="col-md-2">
                <label class="form-label">Discount (%)</label>
                <input class="form-control"
                value="@studentdiscount"
                disabled />
            </div>
            <div class="col-md-2">
                <label class="form-label">Total Fee</label>
                <InputNumber class="form-control"
                @bind-Value="totalFee"
                Disabled="true" />
            </div>
            <div class="col-md-2">
                <label class="form-label">Final Fee</label>
                <input type="text"
                value="@FinalFee"
                class="form-control"
                disabled />
            </div>
            <div class="col-md-2">
                <label class="form-label">Total Paid</label>
                <input type="text"
                value="@paymentInfo.TotalPaid"
                class="form-control"
                disabled />
            </div>
            <div class="col-md-2">
                <label class="form-label">Remaining Amount</label>
                <input type="number"
                value="@paymentInfo.RemainingAmount"
                class="form-control"
                disabled />
            </div>
        </div>

        <!-- Payment Form -->
        <EditForm Model="paymentModel" OnValidSubmit="HandleValidSubmit">
            <DataAnnotationsValidator />
            <ValidationSummary />

            <div class="row mb-3">
                <div class="col-md-4">
                    <label class="form-label">@Localizer["PayAmount"]</label>
                    <InputNumber @bind-Value="paymentModel.PaidAmount"
                    class="form-control" />
                </div>
                <div class="col-md-4">
                    <label class="form-label">@Localizer["PaymentDate"]</label>
                    <InputPersianDatePicker CssClass="form-control" @bind-Value="paymentDate"></InputPersianDatePicker>
                </div>
                <div class="col-md-4 align-self-end">
                    <button type="submit" class="btn btn-primary">@Localizer["SavePayment"]</button>
                </div>
            </div>
        </EditForm>

        @if (!string.IsNullOrEmpty(saveMessage))
        {
            <div class="alert alert-info">@saveMessage</div>
        }
    }
</div>
@if(SelectedStudentId !=0)
{
    <GhalibLanguageAndSkillsCenter.Components.ReUseAbleComponents.StudentPaymentHistory
    StudentId ="@SelectedStudentId" ProgramId="@SelectedProgramId" RefereshToken="LoadHistory"
    ></GhalibLanguageAndSkillsCenter.Components.ReUseAbleComponents.StudentPaymentHistory>
}

@code {
    private int SelectedProgramId { get; set; }
    private string paymentDate { get; set; }

    private int _selectedStudentId;
    public int SelectedStudentId
    {
        get => _selectedStudentId;
        set
        {
            if (_selectedStudentId == value) return;
            _selectedStudentId = value;
            _ = OnStudentChanged(value);
        }
    }

    private List<ProgramModel> programs = new();
    private List<StudentModel> students = new();
    private DiscountModel? discount;
    private string? studentdiscount;
    private int totalFee;
    private List<StudentPaymentModel> paymentDetails = new();
    private StudentPaymentModel paymentInfo = new();
    private string? FinalFee;
    private int LoadHistory = 0;

    // New payment form model and message
    private StudentPaymentModel paymentModel = new();
    private string saveMessage = string.Empty;

    protected override async Task OnInitializedAsync()
    {
        programs = (await ProgramRepo.GetAllAsync("GetAllPrograms"))
           .Where(p => p.IsActive ==1)
           .ToList();

    }

    private async Task OnProgramChanged(int programId)
    {
        SelectedProgramId = programId;
        students = programId > 0
            ? (await StudentRepo.GetAllAsyncById("GetAllStudentsByProgram", new { ProgramId = programId })).ToList()
            : new List<StudentModel>();

        _selectedStudentId = 0;
        discount = null;
        studentdiscount = null;
        totalFee = programs.FirstOrDefault(p => p.ProgramId == programId)?.Fee ?? 0;

        paymentInfo = new StudentPaymentModel();
        saveMessage = string.Empty;

        StateHasChanged();
    }

    private async Task OnStudentChanged(int studentId)
    {
        SelectedStudentId = studentId;
        if (studentId > 0)
        {
            discount = await DiscountRepo.GetByIdAsync("GetStudentDiscount", new { studentid = studentId,ProgramId=SelectedProgramId });
            if(discount != null && discount.IsActive == 1)
            {

                studentdiscount = discount?.DiscountPercentage.ToString();
                FinalFee = (totalFee - (totalFee * discount?.DiscountPercentage / 100)).ToString();
            }
            else
            {
                FinalFee = totalFee.ToString();
                studentdiscount = "No Active Discount";
            }

            paymentDetails = (await PaymentRepo
                        .GetAllAsyncById("GetStudentPaymentHistory", new { studentId }))
                        .ToList();
            paymentInfo = paymentDetails
                .Where(p => p.ProgramId == SelectedProgramId)
                .FirstOrDefault()
              ?? new StudentPaymentModel();

            // Initialize the paymentModel defaults
            paymentModel = new StudentPaymentModel
                {
                    StudentId = studentId,
                    ProgramId = SelectedProgramId,
                    DiscountPercentage = discount?.DiscountPercentage ?? 0,
                    RemainingAmount = paymentInfo.RemainingAmount
                };
        }
        else
        {
            discount = null;
            studentdiscount = null;
            paymentInfo = new StudentPaymentModel();
        }
        saveMessage = string.Empty;
        StateHasChanged();
    }

    private async Task HandleValidSubmit()
    {
        paymentModel.PaymentDate = PersianDateConverter.ToDateTime(paymentDate);
        // Call stored procedure AddStudentPayment
        var outputParams = new Dictionary<string, object?>
        {
            { "@StudentId", SelectedStudentId },
            { "@ProgramId", SelectedProgramId },
            { "@PaidAmount", paymentModel.PaidAmount },
            { "@DiscountPercentage", paymentModel.DiscountPercentage },
            { "@PaymentDate", paymentModel.PaymentDate },
            { "@RemainingAmount", 0 },
            { "@ReturnCode", 0 }
        };

        await PaymentRepo.AddAsync("AddStudentPayment", outputParams);

        // Check return code and remaining
        var returnCode = (int)outputParams["@ReturnCode"]!;
        var remaining = (int)outputParams["@RemainingAmount"]!;
        saveMessage = returnCode == 0
            ? string.Format(Localizer["PaymentSavedSuccessfully"], remaining)
            : string.Format(Localizer["OverpaymentError"], remaining);
            LoadHistory++;

        // Refresh student data
        await OnStudentChanged(SelectedStudentId);


    }
}
